<execution>
  <constraint>
    ## TailAdmin项目技术约束
    - **React 19兼容性**：必须处理新版本的破坏性变更和API调整
    - **TypeScript 5.7严格模式**：所有代码必须通过严格类型检查
    - **Tailwind CSS 4.0语法**：使用最新版本的类名和配置方式
    - **Vite 6.1构建约束**：充分利用新版本的性能优化和特性
    - **ESLint规则严格性**：遵循项目配置的代码质量标准
  </constraint>

  <rule>
    ## 强制开发规范
    - **组件命名规范**：PascalCase，文件名与组件名一致
    - **Hook使用规则**：严格遵循Rules of Hooks，避免条件调用
    - **TypeScript类型规范**：禁止使用any，优先使用接口定义props
    - **Tailwind类名规范**：使用clsx处理条件类名，tailwind-merge处理冲突
    - **国际化强制**：所有用户可见文本必须使用i18next翻译
    - **SVG组件规范**：使用vite-plugin-svgr的ReactComponent导出方式
  </rule>

  <guideline>
    ## 开发指导原则
    - **组件设计优先级**：可复用性 > 性能 > 实现复杂度
    - **状态管理选择**：本地状态 > Context > 外部库
    - **错误处理策略**：优雅降级 > 错误边界 > 全局捕获
    - **性能优化时机**：测量优先 > 过早优化有害
    - **代码组织方式**：功能模块 > 类型分组 > 文件结构清晰
    - **依赖管理原则**：按需引入 > 整体导入 > 减少bundle大小
  </guideline>

  <process>
    ## 标准开发流程
    
    ### Step 1: 需求分析与设计 (30分钟)
    
    ```mermaid
    flowchart TD
        A[需求分析] --> B{组件类型}
        B -->|展示组件| C[Props接口设计]
        B -->|容器组件| D[状态管理设计]
        B -->|布局组件| E[响应式设计]
        C --> F[TypeScript类型定义]
        D --> F
        E --> F
        F --> G[组件结构规划]
    ```
    
    **设计检查清单**：
    - [ ] 组件职责单一明确
    - [ ] Props接口完整类型安全
    - [ ] 状态管理策略合理
    - [ ] 样式方案确定(Tailwind)
    - [ ] 国际化文案规划
    
    ### Step 2: 组件实现 (60-90分钟)
    
    ```mermaid
    graph LR
        A[创建组件文件] --> B[TypeScript接口]
        B --> C[组件实现]
        C --> D[样式处理]
        D --> E[状态逻辑]
        E --> F[事件处理]
        F --> G[错误边界]
    ```
    
    **实现模板**：
    ```typescript
    // 1. 导入依赖
    import React from 'react';
    import { clsx } from 'clsx';
    import { useTranslation } from 'react-i18next';
    
    // 2. 类型定义
    interface ComponentProps {
      // 严格类型定义
    }
    
    // 3. 组件实现
    export const Component: React.FC<ComponentProps> = ({
      // 解构props
    }) => {
      const { t } = useTranslation();
      
      // 状态和逻辑
      
      return (
        // JSX结构
      );
    };
    ```
    
    ### Step 3: 集成测试 (30分钟)
    
    ```mermaid
    graph TD
        A[组件渲染测试] --> B[Props传递验证]
        B --> C[事件处理测试]
        C --> D[状态变化测试]
        D --> E[样式响应测试]
        E --> F[国际化测试]
        F --> G[性能基准测试]
    ```
    
    ### Step 4: 优化与重构 (30分钟)
    
    ```mermaid
    flowchart LR
        A[性能分析] --> B{优化需求}
        B -->|渲染优化| C[React.memo]
        B -->|计算优化| D[useMemo]
        B -->|回调优化| E[useCallback]
        B -->|懒加载| F[Suspense]
        C --> G[验证效果]
        D --> G
        E --> G
        F --> G
    ```
    
    ## TailAdmin特定工作流程
    
    ### 新页面开发流程
    ```mermaid
    graph TD
        A[页面需求] --> B[路由配置]
        B --> C[布局选择AppLayout]
        C --> D[面包屑配置]
        D --> E[页面组件开发]
        E --> F[国际化文案]
        F --> G[侧边栏菜单更新]
    ```
    
    ### 组件库扩展流程
    ```mermaid
    graph TD
        A[组件需求] --> B[设计系统一致性]
        B --> C[Tailwind主题适配]
        C --> D[深色模式支持]
        D --> E[响应式设计]
        E --> F[可访问性支持]
        F --> G[组件文档]
    ```
  </process>

  <criteria>
    ## 代码质量评价标准
    
    ### 功能完整性
    - ✅ 功能需求100%实现
    - ✅ 边界情况正确处理
    - ✅ 错误状态优雅处理
    - ✅ 加载状态用户友好
    
    ### 代码质量
    - ✅ TypeScript零错误零警告
    - ✅ ESLint规则100%通过
    - ✅ 组件可复用性强
    - ✅ 代码可读性好
    
    ### 性能标准
    - ✅ 首屏渲染时间 < 1.5s
    - ✅ 组件重渲染次数最小化
    - ✅ Bundle大小增长合理
    - ✅ 内存泄露零容忍
    
    ### 用户体验
    - ✅ 交互响应及时
    - ✅ 视觉反馈清晰
    - ✅ 无障碍访问支持
    - ✅ 国际化完整支持
    
    ### 维护性
    - ✅ 组件职责边界清晰
    - ✅ 状态管理逻辑简洁
    - ✅ 依赖关系合理
    - ✅ 测试覆盖率充足
  </criteria>
</execution>