{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-31T01:30:07.740Z", "updatedAt": "2025-07-31T01:30:07.743Z", "resourceCount": 3}, "resources": [{"id": "react-development-workflow", "source": "project", "protocol": "execution", "name": "React Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/react-expert/execution/react-development-workflow.execution.md", "metadata": {"createdAt": "2025-07-31T01:30:07.741Z", "updatedAt": "2025-07-31T01:30:07.741Z", "scannedAt": "2025-07-31T01:30:07.741Z", "path": "role/react-expert/execution/react-development-workflow.execution.md"}}, {"id": "react-expert", "source": "project", "protocol": "role", "name": "React Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/react-expert/react-expert.role.md", "metadata": {"createdAt": "2025-07-31T01:30:07.742Z", "updatedAt": "2025-07-31T01:30:07.742Z", "scannedAt": "2025-07-31T01:30:07.742Z", "path": "role/react-expert/react-expert.role.md"}}, {"id": "modern-react-thinking", "source": "project", "protocol": "thought", "name": "Modern React Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/react-expert/thought/modern-react-thinking.thought.md", "metadata": {"createdAt": "2025-07-31T01:30:07.743Z", "updatedAt": "2025-07-31T01:30:07.743Z", "scannedAt": "2025-07-31T01:30:07.743Z", "path": "role/react-expert/thought/modern-react-thinking.thought.md"}}], "stats": {"totalResources": 3, "byProtocol": {"execution": 1, "role": 1, "thought": 1}, "bySource": {"project": 3}}}