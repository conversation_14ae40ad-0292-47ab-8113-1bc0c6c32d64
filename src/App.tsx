import { BrowserRouter as Router, Routes, Route } from "react-router";
import SignIn from "@/pages/AuthPages/SignIn";
import SignUp from "@/pages/AuthPages/SignUp";
import NotFound from "@/pages/OtherPage/NotFound";
import UserProfiles from "@/pages/UserProfiles";
import Videos from "@/pages/UiElements/Videos";
import Images from "@/pages/UiElements/Images";
import Alerts from "@/pages/UiElements/Alerts";
import Badges from "@/pages/UiElements/Badges";
import Avatars from "@/pages/UiElements/Avatars";
import Buttons from "@/pages/UiElements/Buttons";
import LineChart from "@/pages/Charts/LineChart";
import BarChart from "@/pages/Charts/BarChart";
import Calendar from "@/pages/Calendar";
import BasicTables from "@/pages/Tables/BasicTables";
import FormElements from "@/pages/Forms/FormElements";
import Blank from "@/pages/Blank";
import AppLayout from "./layout/AppLayout";
import { ScrollToTop } from "./components/common/ScrollToTop";
import Home from "@/pages/Dashboard/Home";
import UserInfo from "@/pages/User/Info";
import UserProductionStats from "@/pages/User/UserProductionStats";
import InviteCodeManagement from "@/pages/User/InviteCodeManagement";
import Sound from "@/pages/Resource/Sound";
import DigitalHuman from "@/pages/Resource/DigitalHuman";
import RenderLog from "@/pages/RenderLog";
import { Toaster } from "react-hot-toast";

// 自定义 toast 样式
const toastOptions = {
  // 默认设置
  className: "",
  duration: 3000,
  success: {
    style: {
      // 浅绿色
      background: "#22c55e",
      color: "#fff",
      fontSize: "14px",
      fontWeight: "500",
    },
  },
  error: {
    style: {
      background: "#ef4444",
      color: "#fff",
      fontSize: "14px",
      fontWeight: "500",
    },
  },
};

export default function App() {
  return (
    <>
      <Router>
        <ScrollToTop />
        <Routes>
          {/* Dashboard Layout */}
          <Route element={<AppLayout />}>
            <Route index path="/" element={<Home />} />
            {/* User page */}
            <Route path="/user-info" element={<UserInfo />} />
            <Route path="/user-production-stats" element={<UserProductionStats />} />
            <Route path="/invite-code-management" element={<InviteCodeManagement />} />
            {/* Resources */}
            <Route path="/sound" element={<Sound />} />
            <Route path="/digital-human" element={<DigitalHuman />} />
            {/* Render Log */}
            <Route path="/render-log" element={<RenderLog />} />
            {/* Others Page */}
            <Route path="/profile" element={<UserProfiles />} />
            <Route path="/calendar" element={<Calendar />} />

            <Route path="/blank" element={<Blank />} />
            {/* Forms */}
            <Route path="/form-elements" element={<FormElements />} />
            {/* Tables */}
            <Route path="/basic-tables" element={<BasicTables />} />
            {/* Ui Elements */}
            <Route path="/alerts" element={<Alerts />} />
            <Route path="/avatars" element={<Avatars />} />
            <Route path="/badge" element={<Badges />} />
            <Route path="/buttons" element={<Buttons />} />
            <Route path="/images" element={<Images />} />
            <Route path="/videos" element={<Videos />} />
            {/* Charts */}
            <Route path="/line-chart" element={<LineChart />} />
            <Route path="/bar-chart" element={<BarChart />} />
          </Route>

          {/* Auth Layout */}
          <Route path="/signin" element={<SignIn />} />
          <Route path="/signup" element={<SignUp />} />

          {/* Fallback Route */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </Router>
      {/* Toast Notifications */}
      <Toaster
        position="top-center"
        reverseOrder={false}
        gutter={8}
        containerClassName="toast-container"
        containerStyle={{
          zIndex: 99999, // 设置极高的 z-index 确保在最顶层
        }}
        toastOptions={toastOptions}
      />
    </>
  );
}
