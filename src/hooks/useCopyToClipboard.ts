import { useState, useCallback } from "react";
import toast from "react-hot-toast";

interface UseCopyToClipboardOptions {
  successMessage?: string;
  errorMessage?: string;
  onSuccess?: () => void;
  onError?: (error: Error) => void;
  showToast?: boolean;
  position?:
    | "top-left"
    | "top-center"
    | "top-right"
    | "bottom-left"
    | "bottom-center"
    | "bottom-right";
}

interface UseCopyToClipboardReturn {
  copyToClipboard: (text: string) => Promise<boolean>;
  isCopied: boolean;
  error: Error | null;
}

export const useCopyToClipboard = (
  options: UseCopyToClipboardOptions = {}
): UseCopyToClipboardReturn => {
  const [isCopied, setIsCopied] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const {
    successMessage = "Copied",
    errorMessage = "Copy Failed",
    onSuccess,
    onError,
    showToast = true,
    position,
  } = options;

  const copyToClipboard = useCallback(
    async (text: string): Promise<boolean> => {
      try {
        setError(null);
        setIsCopied(false);

        // 检查是否支持 Clipboard API
        if (!navigator.clipboard) {
          throw new Error("Clipboard API not supported");
        }

        await navigator.clipboard.writeText(text);
        setIsCopied(true);

        // 显示成功消息
        if (showToast && successMessage) {
          toast.success(successMessage, {
            duration: 2000, // 成功消息显示3秒
            icon: "✅",
            ...(position && { position }),
          });
        }

        // 执行成功回调
        onSuccess?.();

        // 3秒后重置状态
        setTimeout(() => {
          setIsCopied(false);
        }, 3000);

        return true;
      } catch (err) {
        const error = err instanceof Error ? err : new Error("Unknown error");
        setError(error);

        // 显示错误消息
        if (showToast && errorMessage) {
          toast.error(errorMessage, {
            duration: 3000, // 错误消息显示3秒
            icon: "❌",
            ...(position && { position }),
          });
        }

        // 执行错误回调
        onError?.(error);

        return false;
      }
    },
    [successMessage, errorMessage, onSuccess, onError, showToast, position]
  );

  return {
    copyToClipboard,
    isCopied,
    error,
  };
};

export default useCopyToClipboard;
