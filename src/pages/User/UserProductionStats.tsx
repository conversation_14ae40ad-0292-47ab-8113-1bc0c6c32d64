import { useState } from "react";
import { useTranslation } from "react-i18next";
import toast from "react-hot-toast";
import PageBreadcrumb from "@component/common/PageBreadCrumb";
import PageMeta from "@component/common/PageMeta";
import ComponentCard from "@component/common/ComponentCard";
import Label from "@component/form/Label";
import InputField from "@component/form/input/InputField";
import Select from "@component/ui/select/Selection";
import Button from "@component/ui/button/Button";
import { Table, TableBody, TableCell, TableHeader, TableRow } from "@component/ui/table";
import { ChevronUpIcon } from "@assets/icons";
import { useCopyToClipboard } from "@hooks/useCopyToClipboard";

// 用户制作统计数据类型定义
interface UserProductionData {
  id: number;
  avatar: string;
  username: string;
  userId: string;
  userEmail: string;
  yesterdayProduction: number;
  totalProduction: number;
  status: "active" | "inactive";
}

// 模拟数据
const mockUserProductionData: UserProductionData[] = [
  {
    id: 1,
    avatar: "/images/user/user-01.jpg",
    username: "用户824805",
    userId: "824805",
    userEmail: "186****6623",
    yesterdayProduction: 0,
    totalProduction: 31002,
    status: "active",
  },
  {
    id: 2,
    avatar: "/images/user/user-02.jpg",
    username: "[值] 13612541520",
    userId: "1763480",
    userEmail: "136****1520",
    yesterdayProduction: 0,
    totalProduction: 21176,
    status: "active",
  },
  {
    id: 3,
    avatar: "/images/user/user-03.jpg",
    username: "599",
    userId: "1336226",
    userEmail: "159****0646",
    yesterdayProduction: 2,
    totalProduction: 20898,
    status: "active",
  },
  {
    id: 4,
    avatar: "/images/user/user-04.jpg",
    username: "用户1299733",
    userId: "1299733",
    userEmail: "181****9926",
    yesterdayProduction: 32,
    totalProduction: 13620,
    status: "active",
  },
  {
    id: 5,
    avatar: "/images/user/user-05.jpg",
    username: "线上2",
    userId: "1559675",
    userEmail: "167****9736",
    yesterdayProduction: 0,
    totalProduction: 11585,
    status: "active",
  },
  {
    id: 6,
    avatar: "/images/user/user-06.jpg",
    username: "线上1",
    userId: "1321577",
    userEmail: "186****1249",
    yesterdayProduction: 0,
    totalProduction: 10393,
    status: "active",
  },
];

export default function UserProductionStats() {
  const { t } = useTranslation();
  const [collapsed, setCollapsed] = useState(false);
  const [activeTab, setActiveTab] = useState("digitalAvatar");

  // 使用复制到剪贴板 hook
  const { copyToClipboard } = useCopyToClipboard({
    successMessage: t("pages.userProductionStats.copied"),
    errorMessage: t("pages.userProductionStats.copyFailed"),
  });

  const [queryForm, setQueryForm] = useState({
    conditionField: "",
    conditionValue: "",
  });

  // 功能标签页选项
  const tabOptions = [
    { value: "all", label: t("pages.userProductionStats.tabs.all") },
    {
      value: "digitalAvatar",
      label: t("pages.userProductionStats.tabs.digitalAvatar"),
      active: true,
    },
  ];

  // 条件查询字段选项
  const conditionFieldOptions = [
    {
      value: "username",
      label: t("pages.userProductionStats.conditionField.username"),
    },
    {
      value: "userId",
      label: t("pages.userProductionStats.conditionField.userId"),
    },
    {
      value: "userEmail",
      label: t("pages.userProductionStats.conditionField.userEmail"),
    },
  ];

  const handleQuery = () => {
    console.log("Query:", queryForm);
    // todo: 这里处理查询逻辑

    toast.success(t("pages.userProductionStats.queryComplete"), {
      duration: 2000,
      icon: "🔍",
    });
  };

  const handleReset = () => {
    setQueryForm({
      conditionField: "",
      conditionValue: "",
    });

    toast(t("pages.userProductionStats.formReset"), {
      icon: "🔄",
      duration: 1500,
    });
  };

  const handleConditionFieldChange = (value: string) => {
    setQueryForm({ ...queryForm, conditionField: value });
  };

  const handleUserDetails = (userId: string) => {
    // todo: 跳转到用户详情页面
    console.log("Navigate to user details:", userId);
    toast.success(t("pages.userProductionStats.navigatingToDetails"), {
      duration: 1500,
    });
  };

  return (
    <>
      <PageMeta
        title={t("pages.userProductionStats.title")}
        description={t("pages.userProductionStats.description")}
      />
      <PageBreadcrumb pageTitle={t("pages.userProductionStats.breadcrumb")} />

      <div className="space-y-6">
        {/* 功能标签页 */}
        <ComponentCard title="">
          <div className="border-b border-gray-200 dark:border-white/[0.05]">
            <nav className="-mb-px flex space-x-8 overflow-x-auto">
              {tabOptions.map(tab => (
                <button
                  key={tab.value}
                  onClick={() => setActiveTab(tab.value)}
                  className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.value
                      ? "border-brand-500 text-brand-600 dark:text-brand-400"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </ComponentCard>

        {/* 查询条件区域 */}
        <ComponentCard title={t("pages.userProductionStats.queryConditions")}>
          <div className={`space-y-4 ${collapsed ? "hidden" : ""}`}>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
              {/* 条件查询 - 字段选择 + 输入框 */}
              <div className="md:col-span-2">
                <Label htmlFor="condition">{t("pages.userProductionStats.conditionQuery")}</Label>
                <div className="flex gap-2">
                  <div className="w-36">
                    <Select
                      options={conditionFieldOptions}
                      placeholder={t("pages.userProductionStats.conditionField.select")}
                      onChange={handleConditionFieldChange}
                      value={queryForm.conditionField}
                      size="md"
                      clearable
                    />
                  </div>
                  <div className="flex-1">
                    <InputField
                      type="text"
                      id="condition-value"
                      placeholder={t("pages.userProductionStats.conditionValue.placeholder")}
                      value={queryForm.conditionValue}
                      onChange={e =>
                        setQueryForm({
                          ...queryForm,
                          conditionValue: e.target.value,
                        })
                      }
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 按钮区域 */}
          <div className="flex items-center gap-3 mt-6">
            <Button onClick={handleReset} variant="outline">
              {t("common.reset")}
            </Button>
            <Button onClick={handleQuery}>{t("pages.userProductionStats.query")}</Button>
            <Button
              onClick={() => setCollapsed(!collapsed)}
              variant="outline"
              endIcon={
                <ChevronUpIcon
                  className={`size-4 transition-transform ${collapsed ? "rotate-180" : ""}`}
                />
              }
            >
              {t("pages.userProductionStats.collapse")}
            </Button>
          </div>
        </ComponentCard>

        {/* 用户列表表格 */}
        <ComponentCard title="">
          <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
            <div className="max-w-full overflow-x-auto">
              <Table>
                <TableHeader className="border-b border-gray-100 dark:border-white/[0.05]">
                  <TableRow>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.userProductionStats.username")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.userProductionStats.userId")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.userProductionStats.userEmail")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.userProductionStats.yesterdayProduction")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.userProductionStats.totalProduction")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.userProductionStats.actions")}
                    </TableCell>
                  </TableRow>
                </TableHeader>

                <TableBody className="divide-y divide-gray-100 dark:divide-white/[0.05]">
                  {mockUserProductionData.map(user => (
                    <TableRow key={user.id}>
                      <TableCell className="px-5 py-4 sm:px-6 text-start">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 overflow-hidden rounded-full bg-green-100 flex items-center justify-center">
                            <div className="w-6 h-6 bg-green-500 rounded-full"></div>
                          </div>
                          <span className="font-medium text-gray-800 text-theme-sm dark:text-white/90">
                            {user.username}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-start text-theme-sm">
                        <span
                          className="text-brand-500 hover:underline cursor-pointer"
                          onClick={() => copyToClipboard(user.userId)}
                        >
                          {user.userId}
                        </span>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-start text-theme-sm">
                        <span
                          className="text-brand-500 hover:underline cursor-pointer"
                          onClick={() => copyToClipboard(user.userEmail)}
                        >
                          {user.userEmail}
                        </span>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        {user.yesterdayProduction}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        {user.totalProduction}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-start text-theme-sm">
                        <button
                          className="text-brand-500 hover:underline"
                          onClick={() => handleUserDetails(user.userId)}
                        >
                          {t("pages.userProductionStats.details")}
                        </button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </ComponentCard>
      </div>
    </>
  );
}
