import { useTranslation } from "react-i18next";
import PageMeta from "../../components/common/PageMeta";
import AuthLayout from "./AuthPageLayout";
import SignUpForm from "../../components/auth/SignUpForm";

export default function SignUp() {
  const { t } = useTranslation();

  return (
    <>
      <PageMeta
        title={`${t("auth.signUp.title")} | TailAdmin - React.js Admin Dashboard Template`}
        description={`This is React.js ${t("auth.signUp.title")} page for TailAdmin - React.js Tailwind CSS Admin Dashboard Template`}
      />
      <AuthLayout>
        <SignUpForm />
      </AuthLayout>
    </>
  );
}
