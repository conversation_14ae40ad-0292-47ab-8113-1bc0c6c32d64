import { useState } from "react";
import { useTranslation } from "react-i18next";
import toast from "react-hot-toast";
import PageBreadcrumb from "@component/common/PageBreadCrumb";
import PageMeta from "@component/common/PageMeta";
import ComponentCard from "@component/common/ComponentCard";
import Label from "@component/form/Label";
import InputField from "@component/form/input/InputField";
import Select from "@component/form/Select";
import DatePicker from "@component/form/date-picker";
import Button from "@component/ui/button/Button";
import Badge from "@component/ui/badge/Badge";
import { Table, TableBody, TableCell, TableHeader, TableRow } from "@component/ui/table";
import { ChevronUpIcon } from "../assets/icons";
import { useCopyToClipboard } from "@hooks/useCopyToClipboard";

// 渲染日志数据类型定义
interface RenderLogData {
  id: string;
  fileId: string;
  userId: string;
  fileContent: string;
  status: "success" | "failed" | "generating";
  generateTime: string;
  source: "preview" | "export";
}

// 模拟数据
const mockRenderLogData: RenderLogData[] = [
  {
    fileId: "688058e310b34900301ba3e8",
    id: "67ba0dd05458003f65f95",
    userId: "67ba0dd0545800365f95",
    fileContent: "拉原话不出来的，听好了，今...",
    status: "generating",
    generateTime: "2025-07-23 11:38:28",
    source: "export",
  },
  {
    fileId: "688058abf0b34900301ba334",
    id: "671d54183966d9a003c9e04d",
    userId: "671d54183966d9a003c9e04d1",
    fileContent: "老铁，你先别着急批，我今...",
    status: "generating",
    generateTime: "2025-07-23 11:38:25",
    source: "export",
  },
  {
    fileId: "688059061b34900301ba475",
    id: "667bcfa98168100455f7eb60",
    userId: "667bcfa98168100455f7eb60",
    fileContent: "你知道贵大专业吗？上学毕业...",
    status: "generating",
    generateTime: "2025-07-23 11:38:22",
    source: "preview",
  },
  {
    fileId: "688058215dc42b00440b7e0f",
    id: "67a071a9b4e295004676e8aa0",
    userId: "67a071a9b4e295004676e8aa0",
    fileContent: "给爸爸说收收经说说他解闲得...",
    status: "generating",
    generateTime: "2025-07-23 11:37:54",
    source: "export",
  },
  {
    fileId: "688058e310b34900301ba3e8",
    id: "67ba0dd05458003f65f95",
    userId: "67ba0dd05458003f65f95",
    fileContent: "拉原话不出来的，听好了，今...",
    status: "success",
    generateTime: "2025-07-23 11:37:48",
    source: "preview",
  },
  {
    fileId: "688058b7e978be0030b2dae6",
    id: "679d57c957511400360aabb5f",
    userId: "679d57c957511400360aabb5f",
    fileContent: "喂！喂喂喂！！就近几年来是本...",
    status: "success",
    generateTime: "2025-07-23 11:37:47",
    source: "preview",
  },
  {
    fileId: "688058e1b3db0a003dc8e261",
    id: "66703d618168100442c2364",
    userId: "66703d618168100442c2364",
    fileContent: "天津，做好了理准备，这确实全...",
    status: "generating",
    generateTime: "2025-07-23 11:37:46",
    source: "export",
  },
];

export default function RenderLog() {
  const { t } = useTranslation();
  const [collapsed, setCollapsed] = useState(false);
  const [activeTab, setActiveTab] = useState("digitalAvatar");
  const { copyToClipboard } = useCopyToClipboard({
    successMessage: t("pages.renderLog.copied"),
    errorMessage: t("pages.renderLog.copyFailed"),
  });

  const [queryForm, setQueryForm] = useState({
    conditionField: "",
    conditionValue: "",
    startDate: "",
    endDate: "",
    source: "",
    status: "",
  });

  // 功能标签页选项
  const tabOptions = [
    { value: "all", label: t("pages.renderLog.tabs.all") },
    { value: "digitalAvatar", label: t("pages.renderLog.tabs.digitalAvatar") },
  ];

  // 条件查询字段选项
  const conditionFieldOptions = [
    { value: "", label: t("pages.renderLog.conditionField.select") },
    { value: "userId", label: t("pages.renderLog.conditionField.userId") },
    { value: "userName", label: t("pages.renderLog.conditionField.userName") },
    {
      value: "userEmail",
      label: t("pages.renderLog.conditionField.userEmail"),
    },
  ];

  // 来源选项
  const sourceOptions = [
    { value: "", label: t("pages.renderLog.sources.all") },
    { value: "preview", label: t("pages.renderLog.sources.preview") },
    { value: "export", label: t("pages.renderLog.sources.export") },
  ];

  // 状态选项
  const statusOptions = [
    { value: "", label: t("pages.renderLog.statuses.all") },
    { value: "success", label: t("pages.renderLog.statuses.success") },
    { value: "failed", label: t("pages.renderLog.statuses.failed") },
    { value: "generating", label: t("pages.renderLog.statuses.generating") },
  ];

  const handleQuery = () => {
    console.log("Query:", queryForm);
    toast.success(t("pages.userInfo.queryComplete"), {
      duration: 2000,
      icon: "🔍",
    });
  };

  const handleReset = () => {
    setQueryForm({
      conditionField: "",
      conditionValue: "",
      startDate: "",
      endDate: "",
      source: "",
      status: "",
    });

    toast(t("pages.userInfo.formReset"), {
      icon: "🔄",
      duration: 1500,
    });
  };

  const handleConditionFieldChange = (value: string) => {
    setQueryForm({ ...queryForm, conditionField: value });
  };

  const handleSourceChange = (value: string) => {
    setQueryForm({ ...queryForm, source: value });
  };

  const handleStatusChange = (value: string) => {
    setQueryForm({ ...queryForm, status: value });
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "success":
        return "success";
      case "failed":
        return "error";
      case "generating":
        return "warning";
      default:
        return "light";
    }
  };

  const getSourceBadgeColor = (source: string) => {
    switch (source) {
      case "preview":
        return "info";
      case "export":
        return "primary";
      default:
        return "light";
    }
  };

  const handleViewDetails = (fileId: string) => {
    toast(`${t("pages.renderLog.viewDetails")}: ${fileId}`, {
      icon: "👁️",
      duration: 2000,
    });
  };

  return (
    <>
      <PageMeta title={t("pages.renderLog.title")} description={t("pages.renderLog.description")} />
      <PageBreadcrumb pageTitle={t("pages.renderLog.breadcrumb")} />

      <div className="space-y-6">
        {/* 功能标签页 */}
        <ComponentCard title="">
          <div className="border-b border-gray-200 dark:border-white/[0.05]">
            <nav className="-mb-px flex space-x-8 overflow-x-auto">
              {tabOptions.map(tab => (
                <button
                  key={tab.value}
                  onClick={() => setActiveTab(tab.value)}
                  className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.value
                      ? "border-brand-500 text-brand-600 dark:text-brand-400"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </ComponentCard>

        {/* 查询条件区域 */}
        <ComponentCard title="">
          <div className={`space-y-4 ${collapsed ? "hidden" : ""}`}>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              {/* 条件查询 - 字段选择 + 输入框 */}
              <div className="md:col-span-2">
                <Label htmlFor="condition">{t("pages.renderLog.conditionQuery")}</Label>
                <div className="flex gap-2">
                  <div className="w-36">
                    <Select
                      options={conditionFieldOptions}
                      placeholder={t("pages.renderLog.conditionField.select")}
                      onChange={handleConditionFieldChange}
                      defaultValue={queryForm.conditionField}
                    />
                  </div>
                  <div className="flex-1">
                    <InputField
                      type="text"
                      id="condition-value"
                      placeholder={t("pages.renderLog.conditionValue.placeholder")}
                      value={queryForm.conditionValue}
                      onChange={e =>
                        setQueryForm({
                          ...queryForm,
                          conditionValue: e.target.value,
                        })
                      }
                    />
                  </div>
                </div>
              </div>

              {/* 生成日期 - 开始日期 */}
              <div>
                <DatePicker
                  id="start-date"
                  label={`${t("pages.renderLog.generateDate")} (开始)`}
                  placeholder="2025-06-22"
                  onChange={(_, currentDateString) => {
                    setQueryForm({
                      ...queryForm,
                      startDate: currentDateString,
                    });
                  }}
                />
              </div>

              {/* 生成日期 - 结束日期 */}
              <div>
                <DatePicker
                  id="end-date"
                  label={`${t("pages.renderLog.generateDate")} (结束)`}
                  placeholder="2025-07-23"
                  onChange={(_, currentDateString) => {
                    setQueryForm({ ...queryForm, endDate: currentDateString });
                  }}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              {/* 来源 */}
              <div>
                <Label>{t("pages.renderLog.source")}</Label>
                <Select
                  options={sourceOptions}
                  placeholder={t("pages.renderLog.sources.all")}
                  onChange={handleSourceChange}
                  defaultValue={queryForm.source}
                />
              </div>

              {/* 状态 */}
              <div>
                <Label>{t("pages.renderLog.status")}</Label>
                <Select
                  options={statusOptions}
                  placeholder={t("pages.renderLog.statuses.all")}
                  onChange={handleStatusChange}
                  defaultValue={queryForm.status}
                />
              </div>
            </div>
          </div>

          {/* 按钮区域 */}
          <div className="flex items-center gap-3 mt-6">
            <Button onClick={handleReset} variant="outline">
              {t("pages.renderLog.reset")}
            </Button>
            <Button onClick={handleQuery}>{t("pages.renderLog.query")}</Button>
            <Button
              onClick={() => setCollapsed(!collapsed)}
              variant="outline"
              endIcon={
                <ChevronUpIcon
                  className={`size-4 transition-transform ${collapsed ? "rotate-180" : ""}`}
                />
              }
            >
              {t("pages.renderLog.collapse")}
            </Button>
          </div>
        </ComponentCard>

        {/* 渲染日志列表表格 */}
        <ComponentCard title="">
          <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
            <div className="max-w-full overflow-x-auto">
              <Table>
                <TableHeader className="border-b border-gray-100 dark:border-white/[0.05]">
                  <TableRow>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.renderLog.fileId")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.renderLog.id")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.renderLog.userId")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.renderLog.fileContent")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.renderLog.status")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.renderLog.generateTime")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.renderLog.source")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.renderLog.actions")}
                    </TableCell>
                  </TableRow>
                </TableHeader>

                <TableBody className="divide-y divide-gray-100 dark:divide-white/[0.05]">
                  {mockRenderLogData.map((log, index) => (
                    <TableRow key={`${log.fileId}-${index}`}>
                      <TableCell className="px-5 py-4 sm:px-6 text-start">
                        <span
                          className="text-brand-500 hover:underline cursor-pointer text-theme-sm"
                          onClick={() => copyToClipboard(log.fileId)}
                        >
                          {log.fileId}
                        </span>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-start text-theme-sm">
                        <span
                          className="text-brand-500 hover:underline cursor-pointer"
                          onClick={() => copyToClipboard(log.id)}
                        >
                          {log.id}
                        </span>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-start text-theme-sm">
                        <span
                          className="text-brand-500 hover:underline cursor-pointer"
                          onClick={() => copyToClipboard(log.userId)}
                        >
                          {log.userId}
                        </span>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        <div className="max-w-xs truncate">{log.fileContent}</div>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-start text-theme-sm">
                        <Badge size="sm" color={getStatusBadgeColor(log.status)}>
                          {t(`pages.renderLog.statuses.${log.status}`)}
                        </Badge>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        {log.generateTime}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-start text-theme-sm">
                        <Badge size="sm" color={getSourceBadgeColor(log.source)}>
                          {t(`pages.renderLog.sources.${log.source}`)}
                        </Badge>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-start text-theme-sm">
                        <div className="flex items-center gap-2">
                          <button
                            className="text-brand-500 hover:underline"
                            onClick={() => handleViewDetails(log.fileId)}
                          >
                            {t("pages.renderLog.viewDetails")}
                          </button>
                          <span className="text-gray-300">|</span>
                          <button className="text-gray-400 cursor-not-allowed">
                            {t("pages.renderLog.unavailable")}
                          </button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </ComponentCard>
      </div>
    </>
  );
}
