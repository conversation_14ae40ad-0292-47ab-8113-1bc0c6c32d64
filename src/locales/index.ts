import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";

import en from "./en.json";
import zh from "./zh.json";

const resources = {
  en: {
    translation: en,
  },
  zh: {
    translation: zh,
  },
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: "zh",
    debug: false,
    interpolation: {
      escapeValue: false,
    },
    detection: {
      // 只用路径作判断
      order: [],
      // 禁用缓存 用路径判断即可
      caches: [],
    },
  });

export default i18n;
