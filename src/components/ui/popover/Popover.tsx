import React, { useState, useRef, useEffect } from "react";
import { clsx } from "clsx";

export interface PopoverItem {
  label: string;
  value?: string;
  onClick?: () => void;
  icon?: React.ReactNode;
  disabled?: boolean;
  divider?: boolean; // 添加分割线
}

interface PopoverProps {
  trigger: React.ReactNode; // 触发元素，可以是任何内容
  items: PopoverItem[];
  triggerType?: "click" | "hover"; // 触发方式
  placement?: "bottom-left" | "bottom-right" | "top-left" | "top-right";
  className?: string;
  popoverClassName?: string;
  disabled?: boolean;
  onItemClick?: (item: PopoverItem) => void;
  hoverDelay?: number; // hover 延迟时间（毫秒）
}

export const Popover: React.FC<PopoverProps> = ({
  trigger,
  items,
  triggerType = "click",
  placement = "bottom-left",
  className = "",
  popoverClassName = "",
  disabled = false,
  onItemClick,
  hoverDelay = 200,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const popoverRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLDivElement>(null);
  const hoverTimeoutRef = useRef<number | null>(null);

  // 点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // 确保元素存在
      if (!popoverRef.current || !triggerRef.current) return;
      // 检查点击是否在弹出框或触发器外部
      const isOutsidePopover = !popoverRef.current.contains(event.target as Node);
      const isOutsideTrigger = !triggerRef.current.contains(event.target as Node);

      if (isOutsidePopover && isOutsideTrigger) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // ESC 键关闭
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key !== "Escape") return;
      setIsOpen(false);
    };
    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
    }
    return () => {
      document.removeEventListener("keydown", handleEscape);
    };
  }, [isOpen]);

  // 清除 hover 延迟定时器
  const clearHoverTimeout = () => {
    if (!hoverTimeoutRef.current) return;
    clearTimeout(hoverTimeoutRef.current);
    hoverTimeoutRef.current = null;
  };

  // 点击处理
  const handleTriggerClick = () => {
    if (!disabled && triggerType === "click") {
      setIsOpen(!isOpen);
    }
  };

  // hover 进入处理
  const handleMouseEnter = () => {
    if (!disabled && triggerType === "hover") {
      clearHoverTimeout();
      hoverTimeoutRef.current = setTimeout(() => {
        setIsOpen(true);
      }, hoverDelay);
    }
  };

  // hover 离开处理
  const handleMouseLeave = () => {
    if (!disabled && triggerType === "hover") {
      clearHoverTimeout();
      hoverTimeoutRef.current = setTimeout(() => {
        setIsOpen(false);
      }, hoverDelay);
    }
  };

  // popover 内容区域的 hover 处理
  const handlePopoverMouseEnter = () => {
    if (!disabled && triggerType === "hover") {
      clearHoverTimeout();
    }
  };

  const handlePopoverMouseLeave = () => {
    if (!disabled && triggerType === "hover") {
      clearHoverTimeout();
      hoverTimeoutRef.current = setTimeout(() => {
        setIsOpen(false);
      }, hoverDelay);
    }
  };

  const handleItemClick = (item: PopoverItem) => {
    if (item.disabled) return;
    if (item.onClick) {
      item.onClick();
    }
    if (onItemClick) {
      onItemClick(item);
    }
    setIsOpen(false);
  };

  // 清理定时器
  useEffect(() => {
    return () => {
      clearHoverTimeout();
    };
  }, []);

  // 根据 placement 设置位置样式
  const getPlacementClasses = () => {
    switch (placement) {
      case "bottom-left":
        return "top-full left-0 mt-2";
      case "bottom-right":
        return "top-full right-0 mt-2";
      case "top-left":
        return "bottom-full left-0 mb-2";
      case "top-right":
        return "bottom-full right-0 mb-2";
      default:
        return "top-full left-0 mt-2";
    }
  };

  return (
    <div className={clsx("relative inline-block", className)}>
      {/* 触发元素 */}
      <div
        ref={triggerRef}
        onClick={handleTriggerClick}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className={clsx("cursor-pointer", disabled && "cursor-not-allowed opacity-50")}
      >
        {trigger}
      </div>

      {/* 弹出内容 */}
      {isOpen && (
        <div
          ref={popoverRef}
          onMouseEnter={handlePopoverMouseEnter}
          onMouseLeave={handlePopoverMouseLeave}
          className={clsx(
            "absolute z-50 min-w-48 rounded-xl border border-gray-200 bg-white shadow-theme-lg dark:border-gray-800 dark:bg-gray-900",
            getPlacementClasses(),
            popoverClassName
          )}
        >
          <div className="py-2">
            {items.map((item, index) => (
              <React.Fragment key={index}>
                {/* 分割线 */}
                {item.divider && (
                  <div className="my-1 border-t border-gray-200 dark:border-gray-700" />
                )}
                {/* 菜单项 */}
                <button
                  onClick={() => handleItemClick(item)}
                  disabled={item.disabled}
                  className={clsx(
                    "flex w-full items-center gap-3 px-4 py-2.5 text-left text-sm transition-colors rounded-sm mx-1",
                    item.disabled
                      ? "cursor-not-allowed text-gray-400 dark:text-gray-600"
                      : "text-gray-700 hover:bg-brand-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-brand-500/20 dark:hover:text-brand-300"
                  )}
                >
                  {item.icon && <span className="flex-shrink-0">{item.icon}</span>}
                  <span className="flex-1">{item.label}</span>
                </button>
              </React.Fragment>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Popover;
