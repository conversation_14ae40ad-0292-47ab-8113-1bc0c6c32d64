import React, { useState, useRef, useEffect } from "react";
import { ChevronDownIcon, CloseSmallIcon, CheckSelectedIcon } from "@/assets/icons";
import { clsx } from "clsx";

export interface Option {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface SelectProps {
  options: Option[];
  placeholder?: string;
  onChange: (value: string) => void;
  value?: string;
  defaultValue?: string;
  className?: string;
  disabled?: boolean;
  error?: boolean;
  size?: "sm" | "md" | "lg";
  variant?: "default" | "bordered" | "filled";
  searchable?: boolean;
  clearable?: boolean;
  name?: string;
  id?: string;
}

const Select: React.FC<SelectProps> = ({
  options,
  placeholder = "请选择",
  onChange,
  value,
  defaultValue = "",
  className = "",
  disabled = false,
  error = false,
  size = "md",
  variant = "default",
  searchable = false,
  clearable = false,
  name,
  id,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState<string>(value || defaultValue);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const [searchTerm, setSearchTerm] = useState("");

  const selectRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const listRef = useRef<HTMLUListElement>(null);

  // 同步外部value变化
  useEffect(() => {
    if (value !== undefined) {
      setSelectedValue(value);
    }
  }, [value]);

  // 点击外部关闭下拉
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm("");
        setHighlightedIndex(-1);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // 过滤选项
  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // 获取选中选项的标签
  const selectedOption = options.find(option => option.value === selectedValue);
  const displayValue = selectedOption ? selectedOption.label : "";

  // 处理选项选择
  const handleSelect = (option: Option) => {
    if (option.disabled) return;

    const newValue = option.value;
    setSelectedValue(newValue);
    onChange(newValue);
    setIsOpen(false);
    setSearchTerm("");
    setHighlightedIndex(-1);
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (disabled) return;

    switch (e.key) {
      case "Enter":
      case " ":
        if (!isOpen) {
          setIsOpen(true);
        } else if (highlightedIndex >= 0) {
          handleSelect(filteredOptions[highlightedIndex]);
        }
        e.preventDefault();
        break;
      case "Escape":
        setIsOpen(false);
        setSearchTerm("");
        setHighlightedIndex(-1);
        break;
      case "ArrowDown":
        if (!isOpen) {
          setIsOpen(true);
        } else {
          setHighlightedIndex(prev => (prev < filteredOptions.length - 1 ? prev + 1 : 0));
        }
        e.preventDefault();
        break;
      case "ArrowUp":
        if (isOpen) {
          setHighlightedIndex(prev => (prev > 0 ? prev - 1 : filteredOptions.length - 1));
        }
        e.preventDefault();
        break;
    }
  };

  // 处理搜索
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setHighlightedIndex(-1);
  };

  // 清除选择
  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedValue("");
    onChange("");
  };

  // 样式类名
  const sizeClasses = {
    sm: "h-9 px-3 py-1.5 text-sm",
    md: "h-11 px-4 py-2.5 text-sm",
    lg: "h-12 px-4 py-3 text-base",
  };

  const variantClasses = {
    default: "border border-gray-300 bg-white dark:border-gray-700 dark:bg-gray-900",
    bordered: "border-2 border-gray-300 bg-white dark:border-gray-600 dark:bg-gray-900",
    filled: "border border-transparent bg-gray-100 dark:bg-gray-800",
  };

  const triggerClasses = clsx(
    "relative w-full rounded-lg text-left transition-colors cursor-pointer",
    "focus:outline-none focus:ring-3 focus:ring-brand-500/10",
    sizeClasses[size],
    variantClasses[variant],
    {
      "border-red-300 focus:border-red-500 focus:ring-red-500/10 dark:border-red-700": error,
      "border-brand-300 focus:border-brand-500 dark:border-brand-600": !error && isOpen,
      "hover:border-gray-400 dark:hover:border-gray-600": !error && !isOpen && !disabled,
      "opacity-50 cursor-not-allowed": disabled,
      "text-gray-900 dark:text-white": selectedValue,
      "text-gray-400 dark:text-gray-500": !selectedValue,
    },
    className
  );

  const dropdownClasses = clsx(
    "absolute z-50 mt-1 w-full rounded-lg border border-gray-200 bg-white shadow-lg",
    "dark:border-gray-700 dark:bg-gray-900",
    "max-h-60 overflow-auto"
  );

  //下拉框选项样式
  const optionClasses = (option: Option, index: number) =>
    clsx("relative px-4 py-2.5 text-sm cursor-pointer select-none my-1 mx-1 rounded-sm", {
      "bg-brand-50 text-brand-600 dark:bg-brand-500/10 dark:text-brand-400":
        highlightedIndex === index,
      "bg-brand-100 text-brand-700 dark:bg-brand-500/20 dark:text-brand-300":
        option.value === selectedValue,
      "text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-800":
        highlightedIndex !== index && option.value !== selectedValue && !option.disabled,
      "text-gray-400 cursor-not-allowed": option.disabled,
    });

  return (
    <div ref={selectRef} className="relative">
      {/* Hidden select for form compatibility */}
      {name && (
        <select
          name={name}
          value={selectedValue}
          onChange={() => {}}
          className="sr-only"
          tabIndex={-1}
        >
          <option value="">{placeholder}</option>
          {options.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      )}

      {/* Trigger */}
      <div
        id={id}
        className={triggerClasses}
        onClick={() => !disabled && setIsOpen(!isOpen)}
        onKeyDown={handleKeyDown}
        tabIndex={disabled ? -1 : 0}
        role="combobox"
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        aria-disabled={disabled}
      >
        <span className="block truncate">{displayValue || placeholder}</span>

        <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
          {clearable && selectedValue && !disabled && (
            <button
              onClick={handleClear}
              className="pointer-events-auto mr-1 rounded-full p-0.5 hover:bg-gray-100 dark:hover:bg-gray-800"
              type="button"
            >
              <CloseSmallIcon className="w-4 h-4 text-gray-400" />
            </button>
          )}
          <ChevronDownIcon
            className={clsx("w-5 h-5 text-gray-400 transition-transform duration-200", {
              "rotate-180": isOpen,
            })}
          />
        </span>
      </div>

      {/* Dropdown */}
      {isOpen && (
        <div className={dropdownClasses}>
          {searchable && (
            <div className="p-2 border-b border-gray-200 dark:border-gray-700">
              <input
                ref={inputRef}
                type="text"
                value={searchTerm}
                onChange={handleSearchChange}
                placeholder="搜索选项..."
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md 
                         focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent
                         dark:border-gray-600 dark:bg-gray-800 dark:text-white"
                autoFocus
              />
            </div>
          )}

          <ul ref={listRef} className="py-1" role="listbox" aria-labelledby={id}>
            {filteredOptions.length === 0 ? (
              <li className="px-4 py-2.5 text-sm text-gray-500 dark:text-gray-400">
                没有找到匹配的选项
              </li>
            ) : (
              filteredOptions.map((option, index) => (
                <li
                  key={option.value}
                  className={optionClasses(option, index)}
                  onClick={() => handleSelect(option)}
                  onMouseEnter={() => setHighlightedIndex(index)}
                  role="option"
                  aria-selected={option.value === selectedValue}
                >
                  <span className="block truncate">{option.label}</span>
                  {option.value === selectedValue && (
                    <span className="absolute inset-y-0 right-0 flex items-center pr-4">
                      <CheckSelectedIcon className="w-5 h-5 text-brand-600" />
                    </span>
                  )}
                </li>
              ))
            )}
          </ul>
        </div>
      )}
    </div>
  );
};

export default Select;
