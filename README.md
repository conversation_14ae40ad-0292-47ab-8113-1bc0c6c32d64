# TailAdmin React - Modern Admin Dashboard Template

[![React 19](https://img.shields.io/badge/React-19.0.0-61dafb?style=flat-square&logo=react)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.7.2-3178c6?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind%20CSS-4.0.8-06b6d4?style=flat-square&logo=tailwindcss)](https://tailwindcss.com/)
[![Vite](https://img.shields.io/badge/Vite-6.1.0-646cff?style=flat-square&logo=vite)](https://vitejs.dev/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg?style=flat-square)](https://opensource.org/licenses/MIT)

TailAdmin是一个基于最新技术栈构建的免费开源管理后台模板，为开发者提供构建现代化、数据驱动的管理面板所需的一切组件和功能。

![TailAdmin React.js Dashboard Preview](./banner.png)

## ✨ 核心特性

- 🚀 **最新技术栈** - React 19 + TypeScript 5.7 + Tailwind CSS 4.0
- ⚡ **极速构建** - 基于 Vite 6.1 的闪电构建体验
- 🎨 **现代设计** - 精美的深色/浅色主题切换
- 📱 **响应式布局** - 完美适配桌面端和移动端
- 🌐 **国际化支持** - 内置 i18next 多语言方案
- 🔧 **TypeScript 严格模式** - 完整的类型安全保障
- 📊 **丰富图表** - 集成 ApexCharts 数据可视化
- 🗓️ **日历组件** - 基于 FullCalendar 的日程管理
- 🎯 **拖拽支持** - React DnD 拖拽交互
- 🔐 **认证系统** - 完整的登录注册流程

## 🛠️ 技术栈详解

### 核心框架
- **React 19.0.0** - 最新版本，支持并发特性和 Suspense
- **TypeScript 5.7.2** - 严格类型检查，提升代码质量
- **React Router 7.1.5** - 最新路由系统，支持数据加载

### 构建工具
- **Vite 6.1.0** - 极速构建工具，HMR 热更新
- **PostCSS 8.5.2** - CSS 后处理器
- **ESLint 9.19.0** - 代码质量检查
- **TypeScript ESLint 8.22.0** - TypeScript 专用检查规则

### UI 框架与组件
- **Tailwind CSS 4.0.8** - 原子化 CSS 框架
- **ApexCharts 4.1.0** - 现代化图表库
- **FullCalendar 6.1.15** - 专业日历组件
- **React Hot Toast 2.5.2** - 优雅的通知组件
- **React Helmet Async 2.0.5** - SEO 元数据管理

### 功能增强
- **Swiper 11.2.3** - 轮播组件
- **React DnD 16.0.1** - 拖拽功能
- **React Dropzone 14.3.5** - 文件上传
- **Flatpickr 4.6.13** - 日期选择器
- **@react-jvectormap** - 地图可视化
- **i18next 25.3.2** - 国际化方案

### 开发工具
- **vite-plugin-svgr** - SVG 组件化支持
- **clsx** - 条件样式类名处理
- **tailwind-merge** - Tailwind 类名合并

## 📂 项目结构

```
miuta-ai-admin-node/
├── public/                     # 静态资源文件
│   ├── favicon.png            # 网站图标
│   └── images/                # 图片资源
│       ├── brand/             # 品牌图标
│       ├── cards/             # 卡片图片
│       ├── country/           # 国家图标
│       ├── error/             # 错误页面图片
│       ├── logo/              # Logo 图片
│       └── user/              # 用户头像
├── src/
│   ├── App.tsx                # 主应用组件
│   ├── main.tsx               # 应用入口文件
│   ├── index.css              # 全局样式
│   ├── components/            # 可复用组件
│   │   ├── auth/              # 认证相关组件
│   │   │   ├── SignInForm.tsx
│   │   │   └── SignUpForm.tsx
│   │   ├── charts/            # 图表组件
│   │   │   ├── bar/
│   │   │   └── line/
│   │   ├── common/            # 通用组件
│   │   │   ├── PageBreadCrumb.tsx
│   │   │   ├── ThemeToggleButton.tsx
│   │   │   └── ...
│   │   ├── ecommerce/         # 电商组件
│   │   ├── form/              # 表单组件
│   │   │   ├── form-elements/ # 表单元素
│   │   │   ├── input/         # 输入组件
│   │   │   └── switch/        # 开关组件
│   │   ├── header/            # 头部组件
│   │   ├── tables/            # 表格组件
│   │   ├── ui/                # UI 基础组件
│   │   │   ├── alert/
│   │   │   ├── button/
│   │   │   ├── modal/
│   │   │   └── ...
│   │   └── UserProfile/       # 用户资料组件
│   ├── context/               # React Context
│   │   ├── SidebarContext.tsx # 侧边栏状态
│   │   └── ThemeContext.tsx   # 主题状态
│   ├── hooks/                 # 自定义 Hooks
│   │   ├── useCopyToClipboard.ts
│   │   ├── useGoBack.ts
│   │   └── useModal.ts
│   ├── icons/                 # SVG 图标
│   ├── layout/                # 布局组件
│   │   ├── AppLayout.tsx      # 主布局
│   │   ├── AppHeader.tsx      # 应用头部
│   │   ├── AppSidebar.tsx     # 侧边栏
│   │   └── ...
│   ├── locales/               # 国际化资源
│   │   ├── en.json            # 英文
│   │   ├── zh.json            # 中文
│   │   └── index.ts
│   ├── pages/                 # 页面组件
│   │   ├── AuthPages/         # 认证页面
│   │   ├── Charts/            # 图表页面
│   │   ├── Dashboard/         # 仪表盘
│   │   ├── Forms/             # 表单页面
│   │   ├── Tables/            # 表格页面
│   │   ├── UiElements/        # UI 元素页面
│   │   └── User/              # 用户管理页面
│   ├── svg.d.ts               # SVG 类型声明
│   └── vite-env.d.ts          # Vite 环境类型
├── index.html                 # HTML 模板
├── package.json               # 项目配置
├── vite.config.ts             # Vite 配置
├── tsconfig.json              # TypeScript 配置
├── postcss.config.js          # PostCSS 配置
├── eslint.config.js           # ESLint 配置
└── README.md                  # 项目说明
```

## 🚀 快速开始

### 环境要求

确保您的开发环境满足以下要求：

- **Node.js** 18.x 或更高版本（推荐使用 20.x）
- **npm** 9.x 或 **yarn** 1.22.x 或 **pnpm** 8.x

### 1. 克隆项目

```bash
git clone https://github.com/TailAdmin/free-react-tailwind-admin-dashboard.git
cd free-react-tailwind-admin-dashboard
```

### 2. 安装依赖

```bash
# 使用 npm
npm install 【由于BE系统的只支持npm，所以包管理器只能用npm】
```

> **注意**: 如果遇到 peer dependencies 冲突，项目已配置 `overrides` 来处理 React 19 兼容性问题。

### 3. 可用脚本

| 脚本命令 | 功能说明 | 详细描述 |
|---------|---------|---------|
| `npm run dev` | 启动开发服务器 | 启动 Vite 开发服务器，支持 HMR 热更新 |
| `npm run build` | 构建生产版本 | TypeScript 编译 + Vite 打包优化 |
| `npm run lint` | 代码质量检查 | 运行 ESLint 检查代码规范 |
| `npm run preview` | 预览生产构建 | 本地预览生产版本构建结果 |

### 4. 启动开发

```bash
# 启动开发服务器
npm run dev

# 服务器将在以下地址启动
# ➜  Local:   http://localhost:5173/
# ➜  Network: http://192.168.x.x:5173/
```

### 5. 构建部署

```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

构建完成后，生产文件将输出到 `dist/` 目录。

## ⚙️ 配置说明

### TypeScript 配置

项目使用 TypeScript 严格模式，配置了路径映射：

```typescript
// tsconfig.app.json
{
  "paths": {
    "@/*": ["src/*"],
    "@page/*": ["src/pages/*"],
    "@component/*": ["src/components/*"],
    "@hooks/*": ["src/hooks/*"]
  }
}
```

### Vite 配置亮点

- **SVG 组件化**: 使用 `vite-plugin-svgr` 将 SVG 转换为 React 组件
- **路径别名**: 简化导入路径，提升开发体验
- **React 插件**: 支持 JSX 和热更新

### ESLint 规则

- React Hooks 规则检查
- TypeScript 严格类型检查
- React Refresh 开发体验优化

### 快速链接

- [📱 在线演示 (免费版)](https://free-react-demo.tailadmin.com/)
- [✨ 官方网站](https://tailadmin.com)
- [📄 完整文档](https://tailadmin.com/docs)
- [🎨 Figma 设计稿](https://www.figma.com/community/file/1214477970819985778)
- [⚡ Pro 版本](https://tailadmin.com/pricing)

### 其他版本

- [HTML 版本](https://github.com/TailAdmin/tailadmin-free-tailwind-dashboard-template)
- [Next.js 版本](https://github.com/TailAdmin/free-nextjs-admin-dashboard)
- [Vue.js 版本](https://github.com/TailAdmin/vue-tailwind-admin-dashboard)

## 🧩 组件库概览

TailAdmin 提供了构建现代化管理后台所需的完整组件生态：

### 🔐 认证组件
- **登录表单** (`SignInForm`) - 支持多种登录方式
- **注册表单** (`SignUpForm`) - 完整的用户注册流程
- **认证布局** (`AuthPageLayout`) - 统一的认证页面布局

### 📊 数据可视化
- **ApexCharts 集成** - 现代化图表库
  - 折线图 (`LineChartOne`)
  - 柱状图 (`BarChartOne`)
  - 饼图和环形图
- **数据表格** (`BasicTableOne`) - 功能完整的数据表格
- **统计卡片** (`EcommerceMetrics`) - 数据概览卡片

### 🎨 UI 基础组件
- **按钮系统** (`Button`) - 多种样式和状态
- **表单组件** - 完整的表单生态
  - 输入框 (`InputField`)
  - 选择器 (`Select`, `MultiSelect`)
  - 文件上传 (`FileInput`, `DropZone`)
  - 日期选择 (`date-picker`)
  - 开关按钮 (`Switch`, `ToggleSwitch`)
- **反馈组件**
  - 警告提示 (`Alert`)
  - 通知消息 (React Hot Toast)
  - 模态框 (`Modal`)
- **展示组件**
  - 头像 (`Avatar`)
  - 徽章 (`Badge`)
  - 下拉菜单 (`Dropdown`)

### 🗓️ 高级功能
- **日历组件** - 基于 FullCalendar 的日程管理
- **地图可视化** - @react-jvectormap 地图组件
- **拖拽功能** - React DnD 拖拽交互
- **文件处理** - 拖拽上传和文件管理

### 🌐 国际化与主题
- **多语言支持** - 中英文切换 (i18next)
- **深色模式** - 完整的主题切换系统
- **响应式设计** - 移动端适配

## 📈 版本对比

| 功能特性 | 免费版 | Pro 版 |
|---------|--------|--------|
| 仪表盘页面 | 1 个主仪表盘 | 5+ 专业仪表盘 (分析、电商、营销、CRM、股票) |
| 组件数量 | 30+ 组件 | 400+ 组件和元素 |
| UI 元素 | 50+ 基础元素 | 完整设计系统 |
| 页面模板 | 10+ 页面 | 100+ 页面模板 |
| Figma 设计 | 社区版设计稿 | 完整设计系统 |
| 技术支持 | 社区支持 | 邮件技术支持 |
| 商业使用 | ✅ MIT 协议 | ✅ 商业协议 |
| 源码访问 | ✅ 完整源码 | ✅ 完整源码 |

[🚀 升级到 Pro 版本](https://tailadmin.com/pricing) 获取更多功能和专业支持

## 📋 开发规范

### Git 分支管理规范

#### 分支类型与命名规范

```bash
# 主要分支
prod          # 生产环境分支，只接受 release 分支的合并
dev           # 开发环境分支，所有 feature 分支的基础分支

# 功能开发分支格式：开发者/月日/类型/描述
daixon/2507/feat/user-authentication       # 新功能开发
daixon/2507/fix/login-bug                  # Bug 修复
daixon/2507/docs/update-readme             # 文档更新
daixon/2507/style/component-styling        # 样式调整
daixon/2507/refactor/api-optimization      # 代码重构
daixon/2507/test/unit-test-coverage        # 测试相关
daixon/2507/chore/dependency-update        # 其他维护性工作 (清理代码, 重构文件结构, 配置调整)
```

#### 分支类型说明

| 类型 | 说明 | 示例 |
|------|------|------|
| `feat` | 新功能开发 | `daixon/2507/feat/user-dashboard` |
| `fix` | Bug 修复 | `daixon/2507/fix/memory-leak` |
| `docs` | 文档相关 | `daixon/2507/docs/api-documentation` |
| `style` | 样式修改（不影响功能） | `daixon/2507/style/button-themes` |
| `refactor` | 代码重构 | `daixon/2507/refactor/auth-module` |
| `test` | 测试相关 | `daixon/2507/test/component-testing` |
| `chore` | 其他维护性工作 (清理代码, 重构文件结构, 配置调整) | `daixon/2507/chore/webpack-config` |

#### 分支工作流程

```bash
# 1. 从 dev 分支创建功能分支
git checkout dev
git pull origin dev
git checkout -b daixon/2507/feat/new-feature

# 2. 开发完成后推送分支
git add .
git commit -m "feat(auth): add user login functionality"
git push origin daixon/2507/feat/new-feature

# 3. 创建 Pull Request 到 dev 分支
# 4. 代码审查通过后合并到 dev
# 5. 删除功能分支
git branch -d daixon/2507/feat/new-feature
```

### Commit 提交规范

#### Angular Commit Message 格式

采用 Angular 团队的 commit message 规范：

```
<type>: <subject>

<body>

<footer>
```

#### 提交类型 (type)

| 类型 | 说明 | 示例 |
|------|------|------|
| `feat` | 新功能 | `feat: add user login functionality` |
| `fix` | Bug 修复 | `fix: resolve chart rendering issue` |
| `docs` | 文档更新 | `docs: update installation guide` |
| `style` | 格式修改（不影响代码逻辑） | `style(button): adjust padding and margins` |
| `refactor` | 代码重构 | `refactor: optimize user data fetching` |
| `test` | 测试相关 | `test: add unit tests for login component` |
| `chore` | 其他维护性工作 (清理代码, 重构文件结构, 配置调整) | `chore: update react to version 19` |
| `perf` | 性能优化 | `perf: optimize chart rendering performance` |
| `ci` | CI/CD 相关 | `ci: add automated testing workflow` |
| `build` | 构建系统修改 | `build: update build configuration` |

## 💡 开发指南

### 路径别名使用

项目配置了路径别名，简化导入：

```typescript
// ✅ 推荐写法
import { Button } from '@component/ui/button/Button';
import { useModal } from '@hooks/useModal';
import HomePage from '@page/Dashboard/Home';

// ❌ 避免使用相对路径
import { Button } from '../../../components/ui/button/Button';
```

### 组件开发最佳实践

```typescript
// 1. 使用 TypeScript 严格模式
interface ComponentProps {
  title: string;
  variant?: 'primary' | 'secondary';
  onClick?: () => void;
}

// 2. 使用 React.FC 类型
export const CustomComponent: React.FC<ComponentProps> = ({
  title,
  variant = 'primary',
  onClick
}) => {
  const { t } = useTranslation(); // 3. 国际化支持
  
  return (
    <button
      className={clsx(
        'base-styles',
        variant === 'primary' && 'primary-styles',
        variant === 'secondary' && 'secondary-styles'
      )}
      onClick={onClick}
    >
      {t(title)}
    </button>
  );
};
```

### 样式处理

```typescript
import { clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

// 条件样式处理
const buttonClass = clsx(
  'base-button',
  isActive && 'active-state',
  isDisabled && 'disabled-state'
);

// 处理 Tailwind 类名冲突
const mergedClass = twMerge(
  'p-4 bg-blue-500',
  'p-2 bg-red-500' // 会覆盖前面的样式
);
```

### 主题系统使用

```typescript
import { useTheme } from '@/context/ThemeContext';

const MyComponent = () => {
  const { theme, toggleTheme } = useTheme();
  
  return (
    <div className={clsx(
      'transition-colors duration-200',
      theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-white text-gray-900'
    )}>
      <button onClick={toggleTheme}>
        切换到 {theme === 'dark' ? '浅色' : '深色'} 模式
      </button>
    </div>
  );
};
```

### 性能优化建议

1. **懒加载页面组件**
```typescript
const LazyDashboard = lazy(() => import('@page/Dashboard/Home'));
```

2. **使用 React.memo 优化重渲染**
```typescript
export const OptimizedComponent = React.memo(Component);
```

3. **合理使用 useMemo 和 useCallback**
```typescript
const memoizedValue = useMemo(() => expensiveCalculation(data), [data]);
const memoizedCallback = useCallback(() => doSomething(), [dependency]);
```

## 🔧 故障排除

### 常见问题

#### 1. 依赖安装失败

```bash
# 清除缓存后重新安装
npm cache clean --force
rm -rf node_modules package-lock.json
npm install

# 或使用 legacy peer deps
npm install --legacy-peer-deps
```

#### 2. TypeScript 编译错误

```bash
# 检查 TypeScript 配置
npm run build

# 修复 ESLint 问题
npm run lint
```

#### 3. Tailwind 样式不生效

确保 PostCSS 配置正确：
```js
// postcss.config.js
export default {
  plugins: {
    '@tailwindcss/postcss': {},
  },
}
```

### 性能优化

- **生产构建分析**: 使用 `npm run build` 后查看 `dist/` 目录大小
- **代码分割**: 懒加载非关键组件
- **图片优化**: 使用 WebP 格式和响应式图片

## 📚 学习资源

### 官方文档
- [React 19 官方文档](https://react.dev/)
- [TypeScript 手册](https://www.typescriptlang.org/docs/)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)
- [Vite 指南](https://vitejs.dev/guide/)

### 推荐阅读
- [React 性能优化最佳实践](https://react.dev/learn/render-and-commit)
- [TypeScript 与 React 最佳实践](https://react-typescript-cheatsheet.netlify.app/)
- [Tailwind CSS 组件设计模式](https://tailwindui.com/components)
</div>
